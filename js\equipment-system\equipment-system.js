/**
 * Equipment System
 * Handles character equipment slots, gear stats, and enhancement
 */

window.EquipmentSystem = {
    // System initialization flag
    isInitialized: false,

    // Cache for DOM elements
    elements: {},

    // Reference to detail view handler
    detailView: null,

    // System data
    data: {
        // Define equipment slots based on the game UI image
        slots: [
            // Armor and Weapons
            { id: 'Helmet', name: 'Helmet', type: 'armor' },
            { id: 'Armor', name: 'Armor', type: 'armor' },
            { id: 'Gloves', name: 'Glove<PERSON>', type: 'armor' },
            { id: 'Boots', name: '<PERSON>', type: 'armor' },
            { id: 'Weapon1', name: 'Weapon 1', type: 'weapon' },
            { id: 'Weapon2', name: 'Weapon 2', type: 'weapon' },

            // Rings
            { id: 'Ring1', name: 'Ring 1', type: 'accessory' },
            { id: 'Ring2', name: 'Ring 2', type: 'accessory' },
            { id: 'Ring3', name: 'Ring 3', type: 'accessory' },
            { id: 'Ring4', name: 'Ring 4', type: 'accessory' },

            // Accessories
            { id: 'Amulet', name: 'Amulet', type: 'accessory' },
            { id: 'Earring1', name: 'Earring 1', type: 'accessory' },
            { id: 'Earring2', name: 'Earring 2', type: 'accessory' },
            { id: 'Bracelet1', name: 'Bracelet 1', type: 'accessory' },
            { id: 'Bracelet2', name: 'Bracelet 2', type: 'accessory' },

            // Special Equipment
            { id: 'Bike', name: 'Bike', type: 'vehicle' },
            { id: 'Epaulet', name: 'Epaulet', type: 'accessory' },
            { id: 'Belt', name: 'Belt', type: 'accessory' },
            { id: 'Carnelian', name: 'Carnelian', type: 'accessory' },
            { id: 'Brooch', name: 'Brooch', type: 'accessory' },
            { id: 'Charm', name: 'Charm', type: 'accessory' },
            { id: 'Talisman', name: 'Talisman', type: 'accessory' },

            // Effectors
            { id: 'Effector1', name: 'Effector 1', type: 'effector' },
            { id: 'Effector2', name: 'Effector 2', type: 'effector' },
            { id: 'Effector3', name: 'Effector 3', type: 'effector' },
            { id: 'Effector4', name: 'Effector 4', type: 'effector' },

            // Arcana
            { id: 'Arcana1', name: 'Arcana 1', type: 'arcana' },
            { id: 'Arcana2', name: 'Arcana 2', type: 'arcana' }
        ],

        // Available equipment items database
        availableItems: {
            weapons: [],
            armor: [],
            accessories: []
        },

        // Equipment items (will be populated later)
        equippedItems: {},

        // Currently selected slot for the details panel
        selectedSlot: null,

        // Weapon upgrade data
        weaponUpgrades: {
            // Currently active weapon being upgraded
            activeWeapon: null,

            // Upgrade settings for the active weapon
            settings: {
                grade: 0, // 0-20
                epicOption: {
                    id: null,
                    level: 0 // 0-2
                },
                activeSlots: 1, // 1-3 depending on weapon
                slotOptions: [], // Array of selected slot options
                extremeLevel: 0, // 0-x depending on weapon type
                divineLevel: 0, // 0-15
                weaponId: null
            },

            // Item-specific settings for each weapon
            items: {}
        },

        // UI Image path
        uiImagePath: forceguidesPlannerData.pluginUrl + 'assets/images/equipment/equipment_ui_base.png',

        // Predefined clickable regions for equipment slots (from user coordinates)
        clickableRegions: {
            // Main equipment
            "Helmet": {
                "left": 197.111083984375,
                "top": 123.99652099609375,
                "right": 361.111083984375,
                "bottom": 246.99652099609375,
                "width": 164,
                "height": 123
            },
            "Bike": {
                "left": 372.111083984375,
                "top": 126.99652099609375,
                "right": 535.111083984375,
                "bottom": 184.99652099609375,
                "width": 163,
                "height": 58
            },
            "Epaulet": {
                "left": 369.111083984375,
                "top": 191.99652099609375,
                "right": 538.111083984375,
                "bottom": 246.99652099609375,
                "width": 169,
                "height": 55
            },
            "Belt": {
                "left": 369.111083984375,
                "top": 253.99652099609375,
                "right": 537.111083984375,
                "bottom": 308.99652099609375,
                "width": 168,
                "height": 55
            },
            "Carnelian": {
                "left": 197.111083984375,
                "top": 253.99652099609375,
                "right": 362.111083984375,
                "bottom": 308.99652099609375,
                "width": 165,
                "height": 55
            },
            "Amulet": {
                "left": 22.111083984375,
                "top": 124.99652099609375,
                "right": 188.111083984375,
                "bottom": 183.99652099609375,
                "width": 166,
                "height": 59
            },
            "Armor": {
                "left": 196.111083984375,
                "top": 312.99652099609375,
                "right": 361.111083984375,
                "bottom": 487.99652099609375,
                "width": 165,
                "height": 175
            },
            "Weapon2": {
                "left": 371.111083984375,
                "top": 314.99652099609375,
                "right": 535.111083984375,
                "bottom": 488.99652099609375,
                "width": 164,
                "height": 174
            },
            "Weapon1": {
                "left": 23.111083984375,
                "top": 313.99652099609375,
                "right": 187.111083984375,
                "bottom": 489.99652099609375,
                "width": 164,
                "height": 176
            },
            "Boots": {
                "left": 371.111083984375,
                "top": 494.1076354980469,
                "right": 534.111083984375,
                "bottom": 602.1076354980469,
                "width": 163,
                "height": 108
            },
            "Gloves": {
                "left": 23.111083984375,
                "top": 492.1076354980469,
                "right": 187.111083984375,
                "bottom": 601.1076354980469,
                "width": 164,
                "height": 109
            },
            "Earring1": {
                "left": 197.111083984375,
                "top": 493.1076354980469,
                "right": 278.111083984375,
                "bottom": 542.1076354980469,
                "width": 81,
                "height": 49
            },
            "Earring2": {
                "left": 282.111083984375,
                "top": 495.1076354980469,
                "right": 363.111083984375,
                "bottom": 543.1076354980469,
                "width": 81,
                "height": 48
            },
            "Bracelet1": {
                "left": 197.111083984375,
                "top": 554.1076354980469,
                "right": 279.111083984375,
                "bottom": 600.1076354980469,
                "width": 82,
                "height": 46
            },
            "Bracelet2": {
                "left": 284.111083984375,
                "top": 555.1076354980469,
                "right": 362.111083984375,
                "bottom": 599.1076354980469,
                "width": 78,
                "height": 44
            },
            "Brooch": {
                "left": 196.111083984375,
                "top": 611.2187461853027,
                "right": 361.111083984375,
                "bottom": 669.2187461853027,
                "width": 165,
                "height": 58
            },
            "Effector2": {
                "left": 371.111083984375,
                "top": 617.2187461853027,
                "right": 537.111083984375,
                "bottom": 675.2187461853027,
                "width": 166,
                "height": 58
            },
            "Effector1": {
                "left": 23.111083984375,
                "top": 617.2187461853027,
                "right": 188.111083984375,
                "bottom": 675.2187461853027,
                "width": 165,
                "height": 58
            },
            "Effector3": {
                "left": 23.111083984375,
                "top": 684.2187461853027,
                "right": 187.111083984375,
                "bottom": 742.2187461853027,
                "width": 164,
                "height": 58
            },
            "Effector4": {
                "left": 372.111083984375,
                "top": 684.2187461853027,
                "right": 537.111083984375,
                "bottom": 739.2187461853027,
                "width": 165,
                "height": 55
            },
            "Charm": {
                "left": 196.111083984375,
                "top": 679.2187461853027,
                "right": 362.111083984375,
                "bottom": 736.2187461853027,
                "width": 166,
                "height": 57
            },
            "Arcana1": {
                "left": 369.111083984375,
                "top": 745.2187461853027,
                "right": 535.111083984375,
                "bottom": 805.2187461853027,
                "width": 166,
                "height": 64
            },
            "Arcana2": {
                "left": 24.111083984375,
                "top": 745.2187461853027,
                "right": 188.111083984375,
                "bottom": 805.2187461853027,
                "width": 164,
                "height": 60
            },
            "Talisman": {
                "left": 198.111083984375,
                "top": 745.2187461853027,
                "right": 362.111083984375,
                "bottom": 803.2187461853027,
                "width": 164,
                "height": 58
            },
            "Ring1": {
                "left": 109.111083984375,
                "top": 193.21874618530273,
                "right": 189.111083984375,
                "bottom": 247.21874618530273,
                "width": 80,
                "height": 54
            },
            "Ring2": {
                "left": 23.111083984375,
                "top": 191.21874618530273,
                "right": 105.111083984375,
                "bottom": 246.21874618530273,
                "width": 82,
                "height": 55
            },
            "Ring3": {
                "left": 108.111083984375,
                "top": 253.21874618530273,
                "right": 188.111083984375,
                "bottom": 307.21874618530273,
                "width": 80,
                "height": 54
            },
            "Ring4": {
                "left": 23.111083984375,
                "top": 254.21874618530273,
                "right": 104.111083984375,
                "bottom": 307.21874618530273,
                "width": 81,
                "height": 53
            }
        }
    },

    /**
     * Initialize the system - this is called by the BuildPlanner core
     * when this system becomes active
     */
    init: function() {
        // Prevent multiple initializations
        if (this.isInitialized) {
            return;
        }

        // Get the system panel
        const panel = document.getElementById('fg-equipment-system');
        if (!panel) {
            return;
        }

        // Cache DOM elements for better performance
        this.elements.panel = panel;

        // Ensure StatsConfig is available
        if (typeof StatsConfig === 'undefined') {
            console.log('⚠️ StatsConfig not available yet, retrying initialization in 100ms...');
            setTimeout(() => {
                if (!this.isInitialized) {
                    this.init();
                }
            }, 100);
            return;
        }

        // Load equipment data (should be available via PHP loading)
        this.loadEquipmentData();

        // Initialize UI
        this.initUI();

        // Initialize detail view handler
        this.detailView = window.EquipmentDetailView.init(this);

        // Setup event listeners
        this.setupEventListeners();

        // Create clickable regions based on the predefined coordinates
        this.createClickableRegions();

        // Show initial tooltip to guide users
        this.showTooltipGuide();

        // Load saved data (if any)
        this.loadFromStore();

        // Mark as initialized
        this.isInitialized = true;

        // Wait for BuildPlanner to be fully loaded before updating stats
        this.waitForBuildPlanner(10);
    },

    /**
     * Load equipment data from the data files (loaded via PHP)
     */
    loadEquipmentData: function() {
        // Initialize equipment data if it has an init function
        if (typeof EquipmentData !== 'undefined' && typeof EquipmentData.init === 'function') {
            EquipmentData.init();
        }

        // Import available weapons
        if (typeof EquipmentData !== 'undefined' && EquipmentData.items && EquipmentData.items.weapons) {
            this.data.availableItems.weapons = EquipmentData.items.weapons;
        }

        // Import available armor (combine all armor types)
        if (typeof EquipmentData !== 'undefined' && EquipmentData.items) {
            this.data.availableItems.armor = [
                ...(EquipmentData.items.armor || []),
                ...(EquipmentData.items.helmets || []),
                ...(EquipmentData.items.gloves || []),
                ...(EquipmentData.items.boots || [])
            ];
        }

        // Import available accessories (combine all accessory types)
        if (typeof EquipmentData !== 'undefined' && EquipmentData.items) {
            this.data.availableItems.accessories = [
                ...(EquipmentData.items.rings || []),
                ...(EquipmentData.items.earrings || []),
                ...(EquipmentData.items.bracelets || []),
                ...(EquipmentData.items.amulets || []),
                ...(EquipmentData.items.belts || []),
                ...(EquipmentData.items.carnelians || []),
                ...(EquipmentData.items.talismans || []),
                ...(EquipmentData.items.epaulets || [])
            ];
        }
    },

    /**
     * Initialize the equipment UI
     */
    initUI: function() {
        // Create main layout with equipment image UI and details panel
        const layoutHTML = `
            <div class="fg-equipment-container">
                <div class="fg-equipment-image-container">
                    <div class="fg-equipment-image-overlay" id="fg-equipment-overlay"></div>
                    <img src="${this.data.uiImagePath}" class="fg-equipment-ui-image" id="fg-equipment-ui-image" alt="Equipment UI">
                    <div class="fg-equipment-equipped-items-container"></div>
                </div>

                <div class="fg-equipment-details-panel">
                    <h3 class="fg-equipment-details-title">Equipment Details</h3>
                    <div class="fg-equipment-details-content">
                        <p class="fg-equipment-details-placeholder">Select an equipment slot to view details</p>
                    </div>
                </div>
            </div>

            <!-- Modal for equipment selection (initially hidden) -->
            <div class="fg-equipment-modal" id="fg-equipment-modal">
                <div class="fg-equipment-modal-content">
                    <span class="fg-equipment-modal-close">&times;</span>
                    <h3 id="fg-equipment-modal-title">Equipment Selection</h3>
                    <div id="fg-equipment-modal-body">
                        <p>Select an item to equip in this slot.</p>
                    </div>
                </div>
            </div>
        `;

        // Insert HTML into the panel
        this.elements.panel.innerHTML = layoutHTML;

        // Cache DOM elements
        this.elements.imageContainer = this.elements.panel.querySelector('.fg-equipment-image-container');
        this.elements.uiImage = document.getElementById('fg-equipment-ui-image');
        this.elements.overlay = document.getElementById('fg-equipment-overlay');
        this.elements.equippedItemsContainer = this.elements.panel.querySelector('.fg-equipment-equipped-items-container');
        this.elements.detailsPanel = this.elements.panel.querySelector('.fg-equipment-details-panel');
        this.elements.detailsTitle = this.elements.panel.querySelector('.fg-equipment-details-title');
        this.elements.detailsContent = this.elements.panel.querySelector('.fg-equipment-details-content');
        this.elements.modal = document.getElementById('fg-equipment-modal');
        this.elements.modalTitle = document.getElementById('fg-equipment-modal-title');
        this.elements.modalBody = document.getElementById('fg-equipment-modal-body');
        this.elements.modalClose = this.elements.modal.querySelector('.fg-equipment-modal-close');
    },

    /**
     * Show tooltip guide to help users understand the clickable regions
     */
    showTooltipGuide: function() {
        // Create tooltip element
        const tooltip = document.createElement('div');
        tooltip.className = 'fg-equipment-tooltip';
        tooltip.textContent = 'Click on equipment slots to view details and equip items';

        // Add to container
        this.elements.imageContainer.appendChild(tooltip);

        // Remove after animation completes
        setTimeout(() => {
            if (tooltip && tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        }, 5000); // Match the CSS animation duration
    },

    /**
     * Setup event listeners for the equipment system
     */
    setupEventListeners: function() {
        // Image click event
        this.elements.overlay.addEventListener('click', (e) => {
            // Get click coordinates relative to the image
            const rect = this.elements.uiImage.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Find which region was clicked
            const slotId = this.findClickedRegion(x, y);
            if (slotId) {
                const slot = this.data.slots.find(s => s.id === slotId);
                if (slot) {
                    this.selectSlot(slotId, slot.type);
                }
            }
        });

        // Window resize event for responsive regions
        window.addEventListener('resize', () => {
            this.updateClickableRegionsPositions();
            this.updateEquippedItemsPositions();
        });

        // Close modal when clicking the close button
        if (this.elements.modalClose) {
            this.elements.modalClose.addEventListener('click', () => {
                this.closeEquipmentModal();
            });
        }

        // Close modal when clicking outside of it
        window.addEventListener('click', (e) => {
            if (e.target === this.elements.modal) {
                this.closeEquipmentModal();
            }
        });
    },

    /**
     * Create clickable regions based on the predefined coordinates
     */
    createClickableRegions: function() {
        // Create a DOM element for each clickable region
        const regions = this.data.clickableRegions;
        for (const slotId in regions) {
            // Find slot data
            const slotData = this.data.slots.find(s => s.id === slotId) || {
                id: slotId,
                name: slotId,
                type: 'equipment'
            };

            // Create region element
            const regionElem = document.createElement('div');
            regionElem.id = `fg-rect-${slotId}`;
            regionElem.className = 'fg-equipment-region';
            regionElem.setAttribute('data-slot-id', slotId);
            regionElem.setAttribute('data-slot-type', slotData.type);

            // Add to overlay
            this.elements.overlay.appendChild(regionElem);
        }

        // Make sure the image is loaded before positioning
        if (this.elements.uiImage.complete) {
            this.updateClickableRegionsPositions();
            this.renderEquippedItems();
        } else {
            this.elements.uiImage.onload = () => {
                this.updateClickableRegionsPositions();
                this.renderEquippedItems();
            };
        }
    },

    /**
     * Update positions of clickable regions based on current image size
     * This makes the regions responsive to different screen sizes
     */
    updateClickableRegionsPositions: function() {
        // Get current image dimensions
        const imageRect = this.elements.uiImage.getBoundingClientRect();
        const imageWidth = imageRect.width;
        const imageHeight = imageRect.height;

        // Reference dimensions from the original coordinates
        const refWidth = 560; // Approximate width of the original image
        const refHeight = 820; // Approximate height of the original image

        // Calculate scale factors
        const scaleX = imageWidth / refWidth;
        const scaleY = imageHeight / refHeight;

        // Update each region position
        const regions = this.data.clickableRegions;
        for (const slotId in regions) {
            const region = regions[slotId];
            const regionElem = document.getElementById(`fg-rect-${slotId}`);

            if (regionElem) {
                // Calculate center position
                const centerX = (region.left + region.right) / 2 * scaleX;
                const centerY = (region.top + region.bottom) / 2 * scaleY;

                // Scale the coordinates
                const scaledLeft = region.left * scaleX;
                const scaledTop = region.top * scaleY;
                const scaledWidth = region.width * scaleX;
                const scaledHeight = region.height * scaleY;

                // Position the clickable region (invisible but covers the full area)
                regionElem.style.left = `${scaledLeft}px`;
                regionElem.style.top = `${scaledTop}px`;
                regionElem.style.width = `${scaledWidth}px`;
                regionElem.style.height = `${scaledHeight}px`;
            }
        }

        // Add window resize listener if not already added
        if (!this._resizeListenerAdded) {
            window.addEventListener('resize', () => {
                this.updateClickableRegionsPositions();
            });
            this._resizeListenerAdded = true;
        }
    },

    /**
     * Find which region was clicked based on coordinates
     */
    findClickedRegion: function(x, y) {
        // Get current image dimensions
        const imageRect = this.elements.uiImage.getBoundingClientRect();
        const imageWidth = imageRect.width;
        const imageHeight = imageRect.height;

        // Reference dimensions from the original coordinates
        const refWidth = 560; // Approximate width of the original image
        const refHeight = 820; // Approximate height of the original image

        // Calculate scale factors
        const scaleX = refWidth / imageWidth;
        const scaleY = refHeight / imageHeight;

        // Convert click coordinates to reference coordinate system
        const refX = x * scaleX;
        const refY = y * scaleY;

        // Check each region
        const regions = this.data.clickableRegions;
        for (const slotId in regions) {
            const region = regions[slotId];
            if (refX >= region.left && refX <= region.right &&
                refY >= region.top && refY <= region.bottom) {
                return slotId;
            }
        }
        return null;
    },

    /**
     * Render all equipped items on the equipment UI
     */
    renderEquippedItems: function() {
        // Safety check: ensure container exists
        if (!this.elements.equippedItemsContainer) {
            console.warn('Equipment items container not found, skipping render');
            return;
        }

        // Clear any existing equipped items
        this.elements.equippedItemsContainer.innerHTML = '';

        // Render each equipped item
        for (const slotId in this.data.equippedItems) {
            const item = this.data.equippedItems[slotId];
            this.renderEquippedItem(slotId, item);
        }
    },

    /**
     * Render a single equipped item on the equipment UI
     */
    renderEquippedItem: function(slotId, item) {
        // Get the region coordinates for the slot
        const region = this.data.clickableRegions[slotId];
        if (!region) return;

        // Create item element
        const itemElem = document.createElement('div');
        itemElem.className = 'fg-equipment-item';
        itemElem.setAttribute('data-slot-id', slotId);
        itemElem.setAttribute('data-item-id', item.id);

        // Add item image
        const itemImage = document.createElement('img');
        itemImage.src = item.imagePath;
        itemImage.alt = item.name;
        itemImage.className = 'fg-equipment-item-image';

        itemElem.appendChild(itemImage);

        // Add to container
        this.elements.equippedItemsContainer.appendChild(itemElem);

        // Position the item based on the region
        this.positionEquippedItem(itemElem, region);
    },

    /**
     * Position an equipped item based on its slot region
     */
    positionEquippedItem: function(itemElem, region) {
        // Get current image dimensions
        const imageRect = this.elements.uiImage.getBoundingClientRect();
        const imageWidth = imageRect.width;
        const imageHeight = imageRect.height;

        // Reference dimensions from the original coordinates
        const refWidth = 560; // Approximate width of the original image
        const refHeight = 820; // Approximate height of the original image

        // Calculate scale factors
        const scaleX = imageWidth / refWidth;
        const scaleY = imageHeight / refHeight;

        // Calculate scaled position
        const left = region.left * scaleX;
        const top = region.top * scaleY;
        const width = region.width * scaleX;
        const height = region.height * scaleY;

        // Position the item
        itemElem.style.left = `${left}px`;
        itemElem.style.top = `${top}px`;
        itemElem.style.width = `${width}px`;
        itemElem.style.height = `${height}px`;
    },

    /**
     * Update positions of all equipped items
     */
    updateEquippedItemsPositions: function() {
        const items = this.elements.equippedItemsContainer.querySelectorAll('.fg-equipment-item');
        items.forEach(item => {
            const slotId = item.getAttribute('data-slot-id');
            const region = this.data.clickableRegions[slotId];
            if (region) {
                this.positionEquippedItem(item, region);
            }
        });
    },

    /**
     * Get an icon class based on slot type
     */
    getSlotTypeIcon: function(slotType) {
        // Map slot types to Font Awesome or other icon classes
        const iconMap = {
            'weapon': 'fas fa-sword',
            'armor': 'fas fa-shield-alt',
            'accessory': 'fas fa-gem',
            'vehicle': 'fas fa-motorcycle',
            'effector': 'fas fa-bolt',
            'arcana': 'fas fa-hat-wizard'
        };

        return iconMap[slotType] || 'fas fa-circle';
    },

    /**
     * Select an equipment slot and show details
     */
    selectSlot: function(slotId, slotType) {
        // Find slot data or create a temporary one if using custom names
        let slot = this.data.slots.find(s => s.id === slotId);

        // If this is a custom slot name not in our predefined list
        if (!slot) {
            slot = {
                id: slotId,
                name: slotId, // Use the ID as the name
                type: slotType || 'equipment' // Default type
            };
        }

        // Check if this slot already has an equipped item
        const hasEquippedItem = this.data.equippedItems[slotId] ? true : false;

        // Check if this is the same slot being clicked again within a short time period (double-click behavior)
        const isReclick = (this.data.selectedSlot && this.data.selectedSlot.id === slotId);

        // Mark as selected
        this.data.selectedSlot = slot;

        // Highlight selected slot in the UI
        this.elements.overlay.querySelectorAll('.fg-selected-region').forEach(el => {
            el.classList.remove('fg-selected-region');
        });

        const region = document.getElementById(`fg-rect-${slotId}`);
        if (region) {
            region.classList.add('fg-selected-region');
        }

        // Update details panel using the detail view handler
        this.detailView.updateDetailsPanel(slot);

        // If an item is equipped and this is the first click, only show details
        if (hasEquippedItem && !isReclick) {
            // Store timestamp of this click for double-click tracking
            this.data.lastSlotClickTime = Date.now();
            this.data.lastClickedSlotId = slotId;

            return; // Exit without opening the modal
        }

        // Check if this is a quick double-click on the same item
        // Or if it's an empty slot (always show selection for empty slots)
        const isDoubleClick = isReclick &&
            this.data.lastClickedSlotId === slotId &&
            (Date.now() - this.data.lastSlotClickTime) < 1500; // 1.5 second window

        if (!hasEquippedItem || isDoubleClick) {
            // Open selection modal
            this.openEquipmentModal(slotId, slot.type);
        }
    },

    /**
     * Get the item category key for a given slot ID
     * @param {string} slotId - The slot ID
     * @returns {string} The item category key for EquipmentData.items
     */
    getItemCategoryForSlot: function(slotId) {
        // Handle slots with numbers (Ring1, Ring2, etc.)
        if (slotId.startsWith('Ring')) return 'rings';
        if (slotId.startsWith('Earring')) return 'earrings';
        if (slotId.startsWith('Bracelet')) return 'bracelets';
        if (slotId.startsWith('Weapon')) return 'weapons';
        if (slotId.startsWith('Effector')) return 'effectors';
        if (slotId.startsWith('Arcana')) return 'arcanas';

        // Handle direct slot name mappings
        const slotToCategory = {
            'Bike': 'bikes',
            'Epaulet': 'epaulets',
            'Belt': 'belts',
            'Carnelian': 'carnelians',
            'Talisman': 'talismans',
            'Amulet': 'amulets',
            'Helmet': 'helmets',
            'Armor': 'armor',
            'Gloves': 'gloves',
            'Boots': 'boots'
        };

        return slotToCategory[slotId] || null;
    },

    /**
     * Open the equipment modal for a specific slot
     */
    openEquipmentModal: function(slotId, slotType) {
        // Find slot data
        const slot = this.data.slots.find(s => s.id === slotId) || { id: slotId, name: slotId, type: slotType };

        // Update modal title
        this.elements.modalTitle.textContent = `Select ${slot.name}`;

        // Get items for this slot type
        let availableItems = [];

        // Try to get items using the slot-specific category
        const itemCategory = this.getItemCategoryForSlot(slotId);
        if (itemCategory && EquipmentData.items[itemCategory]) {
            availableItems = EquipmentData.items[itemCategory];
        } else {
            // Fallback to general type filtering
            switch (slotType) {
                case 'weapon':
                    availableItems = this.data.availableItems.weapons;
                    break;
                case 'armor':
                    availableItems = this.data.availableItems.armor;
                    break;
                case 'accessory':
                    availableItems = this.data.availableItems.accessories;
                    break;
                default:
                    availableItems = [];
            }
        }

        // Generate items selection UI
        let itemsHTML = '';
        if (availableItems.length > 0) {
            itemsHTML = '<div class="fg-item-selection-grid">';
            availableItems.forEach(item => {
                itemsHTML += `
                    <div class="fg-item-selection-card" data-item-id="${item.id}">
                        <img src="${item.imagePath}" alt="${item.name}" class="fg-item-selection-image">
                        <div class="fg-item-selection-name">${item.name}</div>
                    </div>
                `;
            });
            itemsHTML += '</div>';
        } else {
            itemsHTML = `<p>No ${slot.name} items available for this slot.</p>`;
        }

        // Update modal body
        this.elements.modalBody.innerHTML = `
            ${itemsHTML}
            <div class="fg-modal-buttons">
                <button class="fg-button fg-modal-close-btn">Cancel</button>
            </div>
        `;

        // Set up event listeners for item selection
        const itemCards = this.elements.modalBody.querySelectorAll('.fg-item-selection-card');
        itemCards.forEach(card => {
            card.addEventListener('click', () => {
                const itemId = card.getAttribute('data-item-id');
                const selectedItem = availableItems.find(item => item.id === itemId);
                if (selectedItem) {
                    this.handleItemSelect(slotId, itemId, selectedItem);
                    this.closeEquipmentModal();
                }
            });
        });

        // Set up event listener for modal close button
        const closeBtn = this.elements.modalBody.querySelector('.fg-modal-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeEquipmentModal();
            });
        }

        // Show the modal
        this.elements.modal.style.display = 'block';
    },

    /**
     * Close the equipment modal
     */
    closeEquipmentModal: function() {
        if (this.elements.modal) {
            this.elements.modal.style.display = 'none';
        }
    },

    /**
     * Handle item selection for a slot
     * @param {string} slotId - The ID of the slot being filled
     * @param {string} itemId - The ID of the selected item
     * @param {Object} itemData - The full data object for the selected item
     */
    handleItemSelect: function(slotId, itemId, itemData) {
        // Prevent equipping to invalid slot
        if (!slotId || !itemId) return;

        // Copy item data and add reference to slot
        const item = JSON.parse(JSON.stringify(itemData));
        item.slotId = slotId;

        // Add the item to equipped items
        this.data.equippedItems[slotId] = item;

        // Initialize weapon-specific settings if this is a weapon
        if (item.type === 'weapon') {
            this.data.weaponUpgrades.settings.weaponId = item.id;
            this.data.weaponUpgrades.activeWeapon = item.id;

            // Reset upgrade settings to defaults when equipping a new weapon
            // But preserve existing settings if they exist for this weapon ID
            if (this.data.weaponUpgrades.settings.weaponId !== item.id) {
                this.data.weaponUpgrades.settings = {
                    weaponId: item.id,
                    grade: 0,
                    epicOption: {
                        id: null,
                        level: 0
                    },
                    activeSlots: 1,
                    slotOptions: new Array(3).fill(null),
                    extremeLevel: 0,
                    divineLevel: 0
                };
            }
        }

        // Update UI
        this.renderEquippedItem(slotId, item);

        // Set this slot as selected in detail panel
        const slot = this.data.slots.find(s => s.id === slotId);
        if (slot) {
            this.selectItem(slotId, item);
        }

        // Update stats
        this.updateStats();
    },

    /**
     * Remove an equipped item
     */
    removeEquippedItem: function(slotId) {
        // Remove item from data
        if (this.data.equippedItems[slotId]) {
            delete this.data.equippedItems[slotId];

            // Remove item from UI
            const itemElem = this.elements.equippedItemsContainer.querySelector(`.fg-equipment-item[data-slot-id="${slotId}"]`);
            if (itemElem) {
                itemElem.remove();
            }

            // Update UI
            const slot = this.data.slots.find(s => s.id === slotId);
            if (slot) {
                this.detailView.updateDetailsPanel(slot);
            }

            // Update stats
            this.updateStats();
        }
    },

    /**
     * Calculate stats from equipped items
     */
    calculateStats: function() {
        const stats = {};

        // Combine stats from all equipped items
        for (const slotId in this.data.equippedItems) {
            const item = this.data.equippedItems[slotId];
            if (item) {
                // Get item-specific settings or create if doesn't exist
                if (!this.data.weaponUpgrades.items) {
                    this.data.weaponUpgrades.items = {};
                }

                if (!this.data.weaponUpgrades.items[slotId]) {
                    this.data.weaponUpgrades.items[slotId] = {
                        weaponId: item.id,
                        grade: 0,
                        epicOption: {
                            id: null,
                            level: 0
                        },
                        activeSlots: 1,
                        slotOptions: new Array(3).fill(null),
                        extremeLevel: 0,
                        divineLevel: 0,
                        chaosLevel: 0
                    };
                }

                // Use the item-specific settings
                const itemSettings = this.data.weaponUpgrades.items[slotId];

                if (item.type === 'weapon') {
                    // For weapons, calculate stats with all upgrades
                    this.addWeaponStatsToTotal(stats, item, itemSettings);
                } else if (item.type === 'belt') {
                    // For belts, use the belt-specific stats calculation
                    this.addBeltStatsToTotal(stats, item, itemSettings);
                } else if (['earring', 'bracelet', 'amulet'].includes(item.type)) {
                    // For accessories with chaos upgrades
                    this.addAccessoryStatsToTotal(stats, item, itemSettings);
                } else if (['armor', 'helmet', 'gloves', 'boots'].includes(item.type)) {
                    // For armor items with base/extreme/divine upgrades
                    this.addArmorStatsToTotal(stats, item, itemSettings);
                } else if (['arcana', 'carnelian', 'talisman', 'epaulet'].includes(item.type)) {
                    // For special items with just base upgrades
                    this.addBaseUpgradeStatsToTotal(stats, item, itemSettings);
                } else {
                    // For other items, just add base stats
                    if (item.baseStats) {
                        for (const statName in item.baseStats) {
                            const statValue = item.baseStats[statName];
                            if (!stats[statName]) {
                                stats[statName] = 0;
                            }
                            stats[statName] += statValue;
                        }
                    }
                }
            }
        }

        return stats;
    },

    /**
     * Add accessory stats with chaos upgrades to total stats
     * @param {Object} stats - The stats object to add to
     * @param {Object} accessory - The accessory item
     * @param {Object} settings - The upgrade settings
     */
    addAccessoryStatsToTotal: function(stats, accessory, settings) {
        // Start with base stats
        if (accessory.baseStats) {
            for (const statName in accessory.baseStats) {
                const statValue = accessory.baseStats[statName];
                if (!stats[statName]) {
                    stats[statName] = 0;
                }
                stats[statName] += statValue;
            }
        }

        // Add chaos upgrade bonuses
        if (settings.chaosLevel > 0) {
            const tier = accessory.chaosTier || 'gold';

            if (window.EquipmentData && EquipmentData.chaosUpgrades &&
                EquipmentData.chaosUpgrades[tier] &&
                settings.chaosLevel < EquipmentData.chaosUpgrades[tier].length) {

                const chaosBonuses = EquipmentData.chaosUpgrades[tier][settings.chaosLevel];

                if (Array.isArray(chaosBonuses)) {
                    chaosBonuses.forEach(bonus => {
                        if (!stats[bonus.stat]) {
                            stats[bonus.stat] = bonus.value;
                        } else {
                            stats[bonus.stat] += bonus.value;
                        }
                    });
                }
            }
        }
    },

    /**
     * Add weapon stats with upgrades to total stats
     * @param {Object} stats - The stats object to add to
     * @param {Object} weapon - The weapon item
     * @param {Object} settings - The upgrade settings
     */
    addWeaponStatsToTotal: function(stats, weapon, settings) {
        // Add base stats with exact upgrade values (not modifiers)
        const weaponType = weapon.subtype || weapon.material || 'orb';

        // Apply base stats with upgrade bonuses
        if (weapon.baseStats) {
            if (weapon.baseStats.attack) {
                const baseAttack = weapon.baseStats.attack;
                const upgradeBonus = window.WeaponsData ? WeaponsData.getUpgradeStat(weaponType, 'attack', settings.grade) : 0;
                stats.attack = (stats.attack || 0) + baseAttack + upgradeBonus;
            }

            if (weapon.baseStats.magicAttack) {
                const baseMagicAttack = weapon.baseStats.magicAttack;
                const upgradeBonus = window.WeaponsData ? WeaponsData.getUpgradeStat(weaponType, 'magicAttack', settings.grade) : 0;
                stats.magicAttack = (stats.magicAttack || 0) + baseMagicAttack + upgradeBonus;
            }

            if (weapon.baseStats.attackRate) {
                const baseAttackRate = weapon.baseStats.attackRate;
                const upgradeBonus = window.WeaponsData ? WeaponsData.getUpgradeStat(weaponType, 'attackRate', settings.grade) : 0;
                stats.attackRate = (stats.attackRate || 0) + baseAttackRate + upgradeBonus;
            }

            // Add other base stats that don't have upgrades
            for (const statName in weapon.baseStats) {
                if (!['attack', 'magicAttack', 'attackRate'].includes(statName)) {
                    if (!stats[statName]) {
                        stats[statName] = 0;
                    }
                    stats[statName] += weapon.baseStats[statName];
                }
            }
        }

        // Add epic option
        const selectedEpicOption = EquipmentData.epicOptions.find(o => o.id === settings.epicOption.id);
        if (selectedEpicOption) {
            const epicValue = selectedEpicOption.levels[settings.epicOption.level]?.value || 0;

            // Use the stat ID directly (no mapping needed since IDs are now consistent)
            const statId = selectedEpicOption.id;

            // Add to stats or create new entry
            if (!stats[statId]) {
                stats[statId] = 0;
            }
            stats[statId] += epicValue;
        }

        // Add slot options
        for (let i = 0; i < settings.activeSlots; i++) {
            const optionId = settings.slotOptions[i];
            if (optionId) {
                const option = EquipmentData.slotOptions.find(o => o.id === optionId);
                if (option) {
                    // Use the stat ID directly (no mapping needed since IDs are now consistent)
                    const statId = option.id;

                    if (!stats[statId]) {
                        stats[statId] = 0;
                    }
                    stats[statId] += option.value;
                }
            }
        }

        // Check if this is a two-handed weapon (extra damage bonuses)
        const isTwoHanded = ['greatsword', 'daikatana'].includes(weapon.subtype);

        // Calculate extreme upgrade bonuses based on level - if applicable
        if (settings.extremeLevel > 0 && EquipmentData.extremeUpgrades) {
            // Get extreme upgrades directly from level7 array
            const extremeUpgrades = EquipmentData.extremeUpgrades.level7 || [];

            // Get the bonuses for the current level
            if (settings.extremeLevel < extremeUpgrades.length) {
                const extremeBonuses = extremeUpgrades[settings.extremeLevel];

                // Apply each bonus, doubling values for two-handed weapons
                extremeBonuses.forEach(bonus => {
                    if (!stats[bonus.stat]) {
                        stats[bonus.stat] = isTwoHanded ? bonus.value * 2 : bonus.value;
                    } else {
                        // For two-handed weapons, double the stat values
                        stats[bonus.stat] += isTwoHanded ? bonus.value * 2 : bonus.value;
                    }
                });
            }
        }

        // Add divine upgrades
        if (settings.divineLevel > 0) {
            // Use the same method as the display function for consistency
            const divineUpgrades = (window.WeaponsData && WeaponsData.divineUpgrades) ?
                WeaponsData.divineUpgrades.high : null;

            if (divineUpgrades) {
                // Convert the divine upgrade data to the same format as extreme
                for (const statName in divineUpgrades) {
                    const statArray = divineUpgrades[statName];
                    if (statArray && settings.divineLevel < statArray.length) {
                        const value = statArray[settings.divineLevel];
                        if (value > 0) {
                            if (!stats[statName]) {
                                stats[statName] = 0;
                            }
                            stats[statName] += value;
                        }
                    }
                }
            }
        }
    },

    /**
     * Add armor stats with upgrades to total stats
     * @param {Object} stats - The stats object to add to
     * @param {Object} armor - The armor item
     * @param {Object} settings - The upgrade settings
     */
    addArmorStatsToTotal: function(stats, armor, settings) {
        // Start by adding base stats with grade modifiers
        const materialType = armor.material || armor.type;
        const gradeUpgrades = EquipmentData.gradeUpgrades[materialType] || EquipmentData.gradeUpgrades.plate || [];

        // Get appropriate modifier based on armor type
        let gradeModifier = { defenseMod: 1, magicDefenseMod: 1, hpMod: 1 };
        if (gradeUpgrades[settings.grade]) {
            gradeModifier = gradeUpgrades[settings.grade];
        }

        // Apply armor base stats with grade modifiers
        if (armor.baseStats) {
            if (armor.baseStats.defense) {
                const defense = Math.floor(armor.baseStats.defense * (gradeModifier.defenseMod || 1));
                stats.defense = (stats.defense || 0) + defense;
            }

            if (armor.baseStats.magicDefense) {
                const magicDefense = Math.floor(armor.baseStats.magicDefense * (gradeModifier.magicDefenseMod || 1));
                stats.magicDefense = (stats.magicDefense || 0) + magicDefense;
            }

            if (armor.baseStats.hpMax) {
                const hpMax = Math.floor(armor.baseStats.hpMax * (gradeModifier.hpMod || 1));
                stats.hpMax = (stats.hpMax || 0) + hpMax;
            }

            // Add other base stats that don't have modifiers
            for (const statName in armor.baseStats) {
                if (!['defense', 'magicDefense', 'hpMax'].includes(statName)) {
                    if (!stats[statName]) {
                        stats[statName] = 0;
                    }
                    stats[statName] += armor.baseStats[statName];
                }
            }
        }

        // Add extreme upgrades if applicable
        if (settings.extremeLevel > 0 && EquipmentData.extremeUpgrades) {
            // Get extreme upgrades directly from level7 array
            const extremeUpgrades = EquipmentData.extremeUpgrades.level7 || [];

            // Get the bonuses for the current level
            if (settings.extremeLevel < extremeUpgrades.length) {
                const extremeBonuses = extremeUpgrades[settings.extremeLevel];

                if (Array.isArray(extremeBonuses)) {
                    extremeBonuses.forEach(bonus => {
                        if (!stats[bonus.stat]) {
                            stats[bonus.stat] = bonus.value;
                        } else {
                            stats[bonus.stat] += bonus.value;
                        }
                    });
                }
            }
        }

        // Add divine upgrades
        if (settings.divineLevel > 0) {
            // Use the same method as the display function for consistency
            const divineUpgrades = (window.WeaponsData && WeaponsData.divineUpgrades) ?
                WeaponsData.divineUpgrades.high : null;

            if (divineUpgrades) {
                // Convert the divine upgrade data to the same format as extreme
                for (const statName in divineUpgrades) {
                    const statArray = divineUpgrades[statName];
                    if (statArray && settings.divineLevel < statArray.length) {
                        const value = statArray[settings.divineLevel];
                        if (value > 0) {
                            if (!stats[statName]) {
                                stats[statName] = 0;
                            }
                            stats[statName] += value;
                        }
                    }
                }
            }
        }
    },

    /**
     * Add basic stats with just base upgrades to total stats
     * @param {Object} stats - The stats object to add to
     * @param {Object} item - The equipment item
     * @param {Object} settings - The upgrade settings
     */
    addBaseUpgradeStatsToTotal: function(stats, item, settings) {
        // Start with base stat values
        if (item.baseStats) {
            // Get appropriate modifier based on upgrade level
            const genericUpgrades = EquipmentData.baseUpgrades.generic || [];
            const gradeModifier = genericUpgrades[settings.grade] || { statMod: 1 };
            const statMod = gradeModifier.statMod || 1;

            // Apply modifier to all base stats
            for (const statName in item.baseStats) {
                const value = Math.floor(item.baseStats[statName] * statMod);
                if (!stats[statName]) {
                    stats[statName] = 0;
                }
                stats[statName] += value;
            }
        }
    },

    /**
     * Update stats in the BuildPlanner core
     */
    updateStats: function() {
        // Calculate all stats from equipped items
        const stats = this.calculateStats();

        // Update BuildPlanner with new stats (if available)
        if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStats) {
            BuildPlanner.updateStats('equipment', stats);
        }
    },

    /**
     * Wait for BuildPlanner to be fully loaded before updating stats
     * Tries multiple times with increasing delays
     */
    waitForBuildPlanner: function(maxAttempts) {
        const self = this;
        let attempts = 0;

        function attemptToRegister() {
            attempts++;

            if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStats) {
                // BuildPlanner is available, update stats
                self.updateStats();
                return true;
            } else if (attempts < maxAttempts) {
                // BuildPlanner not ready, try again after a delay
                setTimeout(attemptToRegister, attempts * 200);
                return false;
            }
            return false;
        }

        // Start the attempt process
        attemptToRegister();
    },

    /**
     * Load data from the central BuildSaverStore
     */
    loadFromStore: function() {
        // Check if BuildSaverStore exists and has data
        if (window.BuildSaverStore && BuildSaverStore.dataLoaded) {
            // Get data for this system
            const systemData = BuildSaverStore.getSystemData('equipment');
            if (systemData) {
                // Use the common loadFromData method
                return this.loadFromData(systemData);
            }
        }

        return false;
    },

    /**
     * Load data directly from provided data object
     * Used by both loadFromStore and the build sharing feature
     */
    loadFromData: function(data) {
        if (data) {
            // Ensure data structure is valid
            this.ensureDataStructure();

            // Load equipped items if available
            if (data.equippedItems) {
                // Clear current equipped items
                this.data.equippedItems = {};

                // Load each equipped item
                for (const slotId in data.equippedItems) {
                    const savedItem = data.equippedItems[slotId];
                    if (savedItem && savedItem.id) {
                        this.data.equippedItems[slotId] = JSON.parse(JSON.stringify(savedItem));
                    }
                }
            }

            // Load weapon upgrade settings if available
            if (data.weaponUpgrades) {
                // Load items structure
                if (data.weaponUpgrades.items) {
                    this.data.weaponUpgrades.items = JSON.parse(JSON.stringify(data.weaponUpgrades.items));
                }

                // Load settings for backward compatibility
                if (data.weaponUpgrades.settings) {
                    this.data.weaponUpgrades.settings = JSON.parse(JSON.stringify(data.weaponUpgrades.settings));
                }
            }

            // Update UI to reflect the loaded data
            // Add a small delay to ensure DOM elements are ready
            if (this.isInitialized) {
                this.renderEquippedItems();
            } else {
                // If not initialized yet, wait a bit and try again
                setTimeout(() => {
                    this.renderEquippedItems();
                }, 100);
            }

            // Update stats
            this.updateStats();

            // Update weapon stats display if there's a selected slot with a weapon
            if (this.data.selectedSlot && this.data.equippedItems[this.data.selectedSlot.id]) {
                const item = this.data.equippedItems[this.data.selectedSlot.id];
                if (item.type === 'weapon' && this.detailView && this.detailView.updateWeaponStats) {
                    this.detailView.updateWeaponStats();
                }
            }

            return true;
        }

        return false;
    },

    /**
     * Get the essential data for saving
     * Returns just the minimum data needed to restore the system state
     */
    getEssentialData: function() {
        // Ensure data structure is valid before providing it
        this.ensureDataStructure();

        // Create clean copies without circular references
        const cleanEquippedItems = {};
        for (const slotId in this.data.equippedItems) {
            const item = this.data.equippedItems[slotId];
            if (item) {
                cleanEquippedItems[slotId] = {
                    id: item.id,
                    name: item.name,
                    type: item.type,
                    imagePath: item.imagePath,
                    baseStats: item.baseStats ? JSON.parse(JSON.stringify(item.baseStats)) : {},
                    upgradeData: item.upgradeData || null,
                    slotId: item.slotId
                };
            }
        }

        // Create clean copy of weapon upgrades
        const cleanWeaponUpgrades = {
            items: JSON.parse(JSON.stringify(this.data.weaponUpgrades.items || {})),
            settings: JSON.parse(JSON.stringify(this.data.weaponUpgrades.settings || {}))
        };

        return {
            equippedItems: cleanEquippedItems,
            weaponUpgrades: cleanWeaponUpgrades
        };
    },

    /**
     * Ensure the proper data structure exists
     */
    ensureDataStructure: function() {
        // Initialize equipped items if it doesn't exist
        if (!this.data.equippedItems) {
            this.data.equippedItems = {};
        }

        // Initialize weapon upgrades structure
        if (!this.data.weaponUpgrades) {
            this.data.weaponUpgrades = {
                activeWeapon: null,
                settings: {
                    grade: 0,
                    epicOption: { id: null, level: 0 },
                    activeSlots: 1,
                    slotOptions: [],
                    extremeLevel: 0,
                    divineLevel: 0,
                    weaponId: null
                },
                items: {}
            };
        }

        // Ensure items object exists
        if (!this.data.weaponUpgrades.items) {
            this.data.weaponUpgrades.items = {};
        }

        // Ensure settings object exists
        if (!this.data.weaponUpgrades.settings) {
            this.data.weaponUpgrades.settings = {
                grade: 0,
                epicOption: { id: null, level: 0 },
                activeSlots: 1,
                slotOptions: [],
                extremeLevel: 0,
                divineLevel: 0,
                weaponId: null
            };
        }
    },

    /**
     * Save current data to the central store
     */
    saveToStore: function() {
        // Ensure data structure is valid
        this.ensureDataStructure();

        if (typeof BuildSaverStore !== 'undefined' && BuildSaverStore.saveData) {
            const essentialData = this.getEssentialData();
            BuildSaverStore.saveData('equipment', essentialData);
            return true;
        }
        return false;
    },

    /**
     * Refresh UI based on current data
     */
    refreshUI: function() {
        // Refresh details panel if a slot is selected
        if (this.data.selectedSlot) {
            this.detailView.updateDetailsPanel(this.data.selectedSlot);
        }

        // Refresh equipped items
        this.renderEquippedItems();
    },

    /**
     * Update the selected item details in the panel
     */
    selectItem: function(slotId, item) {
        if (!this.detailView) return;

        // Store selected item's slot
        const slot = this.data.slots.find(s => s.id === slotId);
        if (!slot) return;

        this.data.selectedSlot = slot;

        // If this is a weapon, ensure weapon settings are initialized
        if (item && item.type === 'weapon') {
            // Set the active weapon ID in the settings for tracking
            this.data.weaponUpgrades.settings.weaponId = item.id;
        }

        // Update details panel
        this.detailView.updateDetailsPanel(slot);
    },

    /**
     * Add belt stats with upgrades to total stats
     * @param {Object} stats - The stats object to add to
     * @param {Object} belt - The belt item
     * @param {Object} settings - The upgrade settings
     */
    addBeltStatsToTotal: function(stats, belt, settings) {
        // Start with base stats
        if (belt.baseStats) {
            for (const statName in belt.baseStats) {
                const statValue = belt.baseStats[statName];
                if (!stats[statName]) {
                    stats[statName] = 0;
                }
                stats[statName] += statValue;
            }
        }

        // Find the corresponding belt data in BeltsData
        if (window.BeltsData && belt.upgradeData) {
            // Get belt data using the reference string
            const beltData = window.BeltsData[belt.upgradeData];

            if (beltData && Array.isArray(beltData.upgrades)) {
                // Get stats for the current level
                const level = settings.grade || 0;
                if (level >= 0 && level < beltData.upgrades.length) {
                    const upgradeStats = beltData.upgrades[level];

                    // Add each stat to the total
                    for (const statName in upgradeStats) {
                        const statValue = upgradeStats[statName];
                        if (statValue) { // Only add non-zero values
                            if (!stats[statName]) {
                                stats[statName] = 0;
                            }
                            stats[statName] += statValue;
                        }
                    }
                }
            }
        }
    },

    /**
     * Check if a stat is percentage-based
     * @param {string} statId - The stat ID to check
     * @returns {boolean} Whether the stat should be displayed as a percentage
     */
    isPercentStat: function(statId) {
        // Always use StatsConfig as the source of truth for stat properties
        if (typeof StatsConfig !== 'undefined') {
            const statInfo = StatsConfig.getStatInfo(statId);
            return statInfo.isPercentage || false;
        }

        // Fallback for backward compatibility
        const percentStats = [
            'critRate', 'critDamage', 'swordSkillAmp', 'magicSkillAmp',
            'resistCritRate', 'resistCritDamage', 'resistSkillAmp'
        ];
        return percentStats.includes(statId);
    },

    /**
     * Format a stat name for display
     * @param {string} statId - The stat ID to format
     * @returns {string} The formatted stat name
     */
    formatStatName: function(statId) {
        // Use StatsConfig as the source of truth for stat names
        if (typeof StatsConfig !== 'undefined') {
            const statInfo = StatsConfig.getStatInfo(statId);
            return statInfo.name || statId;
        }

        // Throw error if StatsConfig not available
        console.error('StatsConfig is required but not available');
        return statId;
    }
};
