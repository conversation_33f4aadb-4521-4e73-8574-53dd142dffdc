/**
 * Honor Medal System
 * Handles honor medal progression with five ranks:
 * - Captain (4 slots)
 * - General (6 slots)
 * - Commander (8 slots)
 * - Hero (10 slots)
 * - Legend (12 slots)
 */

// Check if data is already loaded
(function() {
    // Check if HonorMedalData is already loaded
    if (typeof window.HonorMedalData !== 'undefined') {
        return;
    }

    // Try to load it directly if not already loaded
    function loadScript(url) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = url;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Determine the base URL based on the current script
    const scripts = document.getElementsByTagName('script');
    const currentScript = scripts[scripts.length - 1];
    const currentPath = currentScript.src;

    // Use WordPress plugin URL from global variable instead of attempting to calculate
    const basePath = window.forceguidesPlannerData ? forceguidesPlannerData.pluginUrl : '';

    // Load the data file
    loadScript(basePath + 'js/honor-medal-system/honor-medal-data.js');
})();

// Define the system globally to ensure it's always available
window.HonorMedalSystem = {
    // Track system state
    isInitialized: false,

    // Cache DOM elements
    elements: {},

    // Current active rank
    activeRank: 'captain',

    // Track selected stats and levels for each slot in each rank
    selectedStats: {
        captain: Array(4).fill(null),
        general: Array(6).fill(null),
        commander: Array(8).fill(null),
        hero: Array(10).fill(null),
        legend: Array(12).fill(null)
    },

    // Current selected slot for option popup
    currentSlot: null,

    // Selection window manager
    selectionWindow: null,

    /**
     * Initialize the system - this is called by the BuildPlanner core
     * when this system becomes active
     */
    init: function() {
        // Prevent multiple initializations
        if (this.isInitialized) {
            return;
        }

        // Get the system panel
        const panel = document.getElementById('fg-honor-system');
        if (!panel) {
            return;
        }

        // Cache DOM elements for better performance
        this.elements.panel = panel;

        // Check if data is available
        if (typeof HonorMedalData === 'undefined') {
            // Try again later if data not loaded yet
            setTimeout(() => this.init(), 100);
            return;
        }

        // Initialize UI
        this.initUI();

        // Initialize the selection window manager
        this.selectionWindow = new SelectionWindowManager({
            id: 'fg-honor-medal-selector',
            title: 'Select Honor Medal',
            className: 'fg-honor-medal-selector',
            fixedPosition: true,
            onSelect: (data) => {
                // Check for both camelCase and lowercase versions of the attribute
                const statId = data.statId || data.statid;
                if (statId) {
                    this.selectStat(statId);
                }
            },
            onClose: () => {
                this.currentSlot = null;
            }
        });

        // Load data from the central store if available
        this.loadFromStore();

        // Setup event listeners
        this.setupEventListeners();

        // Set default rank
        this.activeRank = 'captain';

        // Mark as initialized
        this.isInitialized = true;

        // Update stats to reflect initial state
        this.updateHonorMedalStats();
    },

    /**
     * Initialize the user interface
     */
    initUI: function() {
        // Replace the honor medal system placeholder with actual content
        this.createHonorMedalUI();
    },

    // Create the honor medal system UI
    createHonorMedalUI: function() {
        if (!this.elements.panel) {
            return;
        }

        // Create UI for honor medal system
        let rankSectionsHTML = '';

        // Generate sections for each rank
        for (const rankId in HonorMedalData.ranks) {
            const rank = HonorMedalData.ranks[rankId];

            // Check if any medals are equipped in this rank and if they can be leveled up/down
            const canLevelUp = this.canRankLevelUp(rankId);
            const canLevelDown = this.canRankLevelDown(rankId);

            // Calculate average level of medals in this rank
            const rankLevel = this.calculateRankLevel(rankId);
            const rankLevelDisplay = rankLevel > 0 ? ` <span class="level">Lv.${rankLevel}</span>` : '';

            rankSectionsHTML += `
                <div id="${rankId}-rank" class="fg-honor-rank-panel">
                    <div class="fg-honor-rank-header-container">
                        <span class="fg-honor-rank-header">• ${rank.name}${rankLevelDisplay}</span>
                        <button class="fg-honor-rank-level-up" data-rank="${rankId}"
                                title="Level up all medals in this rank" ${!canLevelUp ? 'disabled' : ''}>↑</button>
                        <button class="fg-honor-rank-level-down" data-rank="${rankId}"
                                title="Level down all medals in this rank" ${!canLevelDown ? 'disabled' : ''}>↓</button>
                    </div>
                    <div class="fg-honor-slots-container" id="${rankId}-slots">
                        ${this.createSlotHTML(rankId, rank.slots)}
                    </div>
                </div>
            `;
        }

        const honorSystemHTML = `
            <div class="fg-honor-system-container">
                <h2>Honor Medal System</h2>

                <!-- All rank content stacked vertically -->
                <div class="fg-honor-rank-content">
                    ${rankSectionsHTML}
                </div>

                <!-- Selected stats summary -->
                <div class="fg-honor-selected-stats">
                    <h3>Selected Honor Medal Stats</h3>
                    <div class="fg-honor-selected-list">
                        <!-- Selected stats will be shown here -->
                    </div>
                </div>
            </div>
        `;

        // Replace placeholder with our UI
        this.elements.panel.innerHTML = honorSystemHTML;

        // Cache additional elements
        this.elements.selectedList = this.elements.panel.querySelector('.fg-honor-selected-list');

        // Cache slot containers
        this.elements.slotContainers = {};
        for (const rankId in HonorMedalData.ranks) {
            this.elements.slotContainers[rankId] = this.elements.panel.querySelector(`#${rankId}-slots`);
        }

        // Initialize the selection window manager
        this.selectionWindow = new SelectionWindowManager({
            id: 'fg-honor-medal-selector',
            title: 'Select Honor Medal',
            className: 'fg-honor-medal-selector',
            fixedPosition: true,
            onSelect: (data) => {
                if (data.statId) {
                    this.selectStat(data.statId);
                }
            },
            onClose: () => {
                this.currentSlot = null;
            }
        });

        // Initialize Quick Fill Button Manager
        if (typeof QuickFillButtonConfigs !== 'undefined') {
            this.quickFillManager = QuickFillButtonConfigs.initializeSystem('honor', this.elements.panel.querySelector('.fg-honor-system-container'));
        }

        this.setupSlotClickHandlers();
    },

    // Create HTML for honor medal slots
    createSlotHTML: function(rankId, numSlots) {
        let html = '';

        for (let i = 0; i < numSlots; i++) {
            const slotIndex = i;
            const hasSelection = this.selectedStats[rankId][slotIndex] !== null;

            html += `
                <div class="fg-honor-slot ${hasSelection ? 'selected' : 'empty'}"
                     data-rank="${rankId}"
                     data-slot="${slotIndex}">
                    ${this.getSlotContentHTML(rankId, slotIndex)}
                </div>
            `;
        }

        return html;
    },

    // Get the content HTML for a slot
    getSlotContentHTML: function(rankId, slotIndex) {
        const slotData = this.selectedStats[rankId][slotIndex];

        if (!slotData) {
            return `<div class="fg-honor-slot-empty"></div>`;
        }

        const { statId, level } = slotData;

        // Find the stat definition
        const statDef = this.findStatDefinition(rankId, statId);
        if (!statDef) return `<div class="fg-honor-slot-empty"></div>`;

        // Get stat name from StatsConfig
        let statName = statId;
        if (typeof StatsConfig !== 'undefined') {
            const statInfo = StatsConfig.getStatInfo(statId);
            if (statInfo) {
                statName = statInfo.name;
            }
        }

        // Get stat value at current level
        const statValue = statDef.values[level - 1];

        // Get icon HTML - ensure StatsConfig is available
        let iconHtml = '';
        if (typeof StatsConfig !== 'undefined') {
            try {
                const iconUrl = StatsConfig.getStatIconUrl(statId);
                iconHtml = `<div class="fg-honor-slot-stat-icon"><img src="${iconUrl}" alt="${statName}" onerror="this.onerror=null; this.style.display='none';"></div>`;
            } catch (e) {}
        }

        // Get stat suffix for proper display
        const suffix = this.getStatSuffix(statId);

        return `
            <div class="fg-honor-slot-stat">
                <button class="fg-honor-slot-remove" data-rank="${rankId}" data-slot="${slotIndex}" title="Remove medal">&times;</button>
                ${iconHtml}
                <div class="fg-honor-slot-details">
                    <span class="fg-honor-slot-stat-value">+${statValue}${suffix}</span>
                </div>
            </div>
        `;
    },

    // Selection window is now handled by SelectionWindowManager

    /**
     * Show the stat selection popup for a specific slot
     */
    showStatSelectionPopup: function(rankId, slotIndex) {
        // Check if rank has available stats
        if (!HonorMedalData.rankStats[rankId] || HonorMedalData.rankStats[rankId].length === 0) {
            // No stats available for this rank yet
            return;
        }

        // Set current slot for reference
        this.currentSlot = { rankId, slotIndex };

        // Populate options from rank stats, sorted by chance in ascending order (lowest first)
        const rankStatOptions = [...HonorMedalData.rankStats[rankId]].sort((a, b) => a.chance - b.chance);

        // Prepare options for the selection window
        const options = rankStatOptions.map(stat => {
            // Get stat name from StatsConfig
            let statName = stat.id;
            if (typeof StatsConfig !== 'undefined') {
                const statInfo = StatsConfig.getStatInfo(stat.id);
                if (statInfo) {
                    statName = statInfo.name;
                }
            }

            // Get icon HTML with error handling and fallback
            let iconHtml = '';
            if (typeof StatsConfig !== 'undefined') {
                try {
                    const iconUrl = StatsConfig.getStatIconUrl(stat.id);
                    iconHtml = `<img src="${iconUrl}" alt="${statName}" class="fg-selection-option-icon" onerror="this.onerror=null; this.style.display='none';">`;
                } catch (e) {}
            }

            // Format the chance percentage
            const chanceText = `<span class="chance">(${stat.chance}%)</span>`;

            // Get stat value at level 1 and max level
            const initialValue = stat.values[0];
            const maxValue = stat.values[stat.values.length - 1];

            // Get stat suffix for proper display
            const suffix = this.getStatSuffix(stat.id);

            return {
                html: `
                    <div class="fg-selection-option-with-icon">
                        ${iconHtml}
                        <span class="fg-selection-option-name">${statName} ${chanceText}</span>
                    </div>
                    <div class="fg-selection-option-value">
                        +${initialValue}${suffix} ~ +${maxValue}${suffix}
                    </div>
                `,
                data: {
                    statId: stat.id
                }
            };
        });

        // Show the selection window with a custom title that includes the rank and slot
        this.selectionWindow.show({
            title: `Select Medal for ${HonorMedalData.ranks[rankId].name} Slot ${slotIndex + 1}`,
            options: options
        });
    },

    /**
     * Hide the stat selection popup
     */
    hideStatSelectionPopup: function() {
        this.selectionWindow.hide();
        this.currentSlot = null;
    },

    /**
     * Set up event listeners for user interactions
     */
    setupEventListeners: function() {
        // Set up slot click handlers
        this.setupSlotClickHandlers();

        // Set up event delegation for remove buttons and rank level-up buttons using panel as container
        if (this.elements.panel) {
            this.elements.panel.addEventListener('click', (e) => {
                // Handle remove button clicks
                if (e.target.classList.contains('fg-honor-slot-remove')) {
                    e.stopPropagation(); // Prevent slot click
                    const rankId = e.target.getAttribute('data-rank');
                    const slotIndex = parseInt(e.target.getAttribute('data-slot'));
                    this.removeStat(rankId, slotIndex);
                }

                // Handle rank level-up button clicks
                if (e.target.classList.contains('fg-honor-rank-level-up')) {
                    e.stopPropagation();
                    const rankId = e.target.getAttribute('data-rank');
                    this.levelUpRank(rankId);
                }

                // Handle rank level-down button clicks
                if (e.target.classList.contains('fg-honor-rank-level-down')) {
                    e.stopPropagation();
                    const rankId = e.target.getAttribute('data-rank');
                    this.levelDownRank(rankId);
                }
            });
        }

        // No need for popup event handlers as they're handled by SelectionWindowManager
    },

    /**
     * Set up click handlers for honor medal slots
     */
    setupSlotClickHandlers: function() {
        // Use event delegation for slot clicks
        for (const rankId in this.elements.slotContainers) {
            const container = this.elements.slotContainers[rankId];
            if (!container) continue;

            container.addEventListener('click', (e) => {
                const slot = e.target.closest('.fg-honor-slot');
                if (!slot) return;

                // Don't trigger slot click if the remove button was clicked
                if (e.target.classList.contains('fg-honor-slot-remove') ||
                    e.target.closest('.fg-honor-slot-remove')) {
                    return;
                }

                const rankId = slot.getAttribute('data-rank');
                const slotIndex = parseInt(slot.getAttribute('data-slot'));

                // Get the slot data
                const slotData = this.selectedStats[rankId][slotIndex];

                // If the slot is empty, show the stat selection popup
                if (!slotData) {
                    this.showStatSelectionPopup(rankId, slotIndex);
                }
                // If slot has stat, just show a tooltip or do nothing
                // We no longer level up individual slots when clicked
            });
        }
    },

    // Find a stat definition in the rank's available stats
    findStatDefinition: function(rankId, statId) {
        const rankStats = HonorMedalData.rankStats[rankId];
        if (!rankStats) return null;

        return rankStats.find(stat => stat.id === statId);
    },

    // Quick fill functionality for testing
    quickFill: function() {
        // Define desired stats for each rank
        const desiredStats = {
            captain: 'dex',
            general: 'critDamage',
            commander: 'allAttackUp',
            hero: 'penetration',
            legend: 'allAttackUp'
        };

        // Fill all ranks with their desired stats
        for (const rankId in desiredStats) {
            const statId = desiredStats[rankId];
            const numSlots = HonorMedalData.ranks[rankId].slots;

            // Get the stat definition from rank stats
            const rankStats = HonorMedalData.rankStats[rankId];
            if (!rankStats) continue;

            // Find the desired stat in available options
            const statDef = rankStats.find(stat => stat.id === statId);
            if (!statDef) continue;

            // Fill all slots in this rank
            for (let i = 0; i < numSlots; i++) {
                this.selectedStats[rankId][i] = {
                    statId: statId,
                    level: HonorMedalData.maxLevel // Set to max level
                };

                // Update the slot UI
                this.updateSlotUI(rankId, i);
            }

            // Update the rank level-up button state
            this.updateRankLevelUpButton(rankId);

            // Update the rank level display
            this.updateRankLevelDisplay(rankId);
        }

        // Update displays and save
        this.updateSelectedStatsDisplay();
        this.updateHonorMedalStats();
        this.saveToStore();
    },

    /**
     * Select a stat for the current slot
     */
    selectStat: function(statId) {
        if (!this.currentSlot) return;

        const { rankId, slotIndex } = this.currentSlot;

        // Find the stat in the rank options
        const statDef = this.findStatDefinition(rankId, statId);
        if (!statDef) return;

        // Get the current rank level to set new medal at same level as others
        const rankLevel = this.calculateRankLevel(rankId);
        // Use rank level if medals exist, otherwise start at level 1
        const initialLevel = rankLevel > 0 ? rankLevel : 1;

        // Update selected stats with appropriate level
        this.selectedStats[rankId][slotIndex] = {
            statId: statId,
            level: initialLevel
        };

        // Update the slot UI
        this.updateSlotUI(rankId, slotIndex);

        // Update selected stats display
        this.updateSelectedStatsDisplay();

        // Update stats in the main planner
        this.updateHonorMedalStats();

        // Update the rank level-up button state
        this.updateRankLevelUpButton(rankId);

        // Update the rank level display
        this.updateRankLevelDisplay(rankId);

        // Save to store
        this.saveToStore();
    },

    // Update the UI for a specific slot
    updateSlotUI: function(rankId, slotIndex) {
        const slotElement = document.querySelector(`.fg-honor-slot[data-rank="${rankId}"][data-slot="${slotIndex}"]`);
        if (!slotElement) return;

        // Update class
        if (this.selectedStats[rankId][slotIndex]) {
            slotElement.classList.add('selected');
            slotElement.classList.remove('empty');
        } else {
            slotElement.classList.remove('selected');
            slotElement.classList.add('empty');
        }

        // Update content
        slotElement.innerHTML = this.getSlotContentHTML(rankId, slotIndex);
    },

    // Get appropriate suffix for stat display
    getStatSuffix: function(statId) {
        // Use StatsConfig for percentage determination
        if (typeof StatsConfig !== 'undefined') {
            const statInfo = StatsConfig.getStatInfo(statId);
            if (statInfo && typeof statInfo.isPercentage !== 'undefined') {
                return statInfo.isPercentage ? '%' : '';
            }
        }

        // If we can't determine, return empty suffix
        return '';
    },

    /**
     * Update the selected stats display
     */
    updateSelectedStatsDisplay: function() {
        if (!this.elements.selectedList) {
            this.elements.selectedList = document.querySelector('.fg-honor-selected-list');
            if (!this.elements.selectedList) return;
        }

        // Build combined stats object
        const honorStats = {};

        // Process each rank
        for (const rankId in this.selectedStats) {
            this.selectedStats[rankId].forEach(slotData => {
                if (!slotData) return; // Skip empty slots

                const { statId, level } = slotData;

                // Find the stat definition
                const statDef = this.findStatDefinition(rankId, statId);
                if (!statDef) return;

                // Get the value at current level
                const value = statDef.values[level - 1];

                // Add to total
                honorStats[statId] = (honorStats[statId] || 0) + value;
            });
        }

        // Use StatIntegrationService to generate uniform display
        if (typeof StatIntegrationService !== 'undefined') {
            this.elements.selectedList.innerHTML = StatIntegrationService.createStatSummaryHTML(honorStats);
        } else {
            this.elements.selectedList.innerHTML = '<p class="no-stats">No honor medals selected yet.</p>';
        }
    },

    // Update honor medal stats in the main planner
    updateHonorMedalStats: function() {
        // Combine all selected stats into a single object
        const honorStats = {};

        // Process each rank
        for (const rankId in this.selectedStats) {
            // Add up stats from all slots in this rank
            this.selectedStats[rankId].forEach(slotData => {
                if (!slotData) return; // Skip empty slots

                const { statId, level } = slotData;

                // Find the stat definition
                const statDef = this.findStatDefinition(rankId, statId);
                if (!statDef) return;

                // Get the value at current level
                const value = statDef.values[level - 1];

                // Add to total
                if (honorStats[statId]) {
                    honorStats[statId] += value;
                } else {
                    honorStats[statId] = value;
                }
            });
        }

        // Update main planner stats
        if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStats) {
            BuildPlanner.updateStats('honor', honorStats);

            // Force DPS recalculation if DPSCalculator exists
            if (typeof DPSCalculator !== 'undefined' && DPSCalculator.calculateAndUpdateDPS && BuildPlanner.totalStats) {
                DPSCalculator.calculateAndUpdateDPS(BuildPlanner.totalStats);
            }
        }
    },

    /**
     * Remove a stat from a slot
     */
    removeStat: function(rankId, slotIndex) {
        // Clear the selected stat for this slot
        this.selectedStats[rankId][slotIndex] = null;

        // Update the slot UI
        this.updateSlotUI(rankId, slotIndex);

        // Update selected stats display
        this.updateSelectedStatsDisplay();

        // Update stats in the main planner
        this.updateHonorMedalStats();

        // Update level up button state
        this.updateRankLevelUpButton(rankId);

        // Update the rank level display
        this.updateRankLevelDisplay(rankId);

        // Save to store
        this.saveToStore();
    },

    /**
     * Check if any medals in a rank can be leveled up
     */
    canRankLevelUp: function(rankId) {
        // Check if there are any active medals below max level
        return this.selectedStats[rankId].some(slotData =>
            slotData !== null && slotData.level < HonorMedalData.maxLevel
        );
    },

    /**
     * Check if any medals in a rank can be leveled down
     */
    canRankLevelDown: function(rankId) {
        // Check if there are any active medals above level 1
        return this.selectedStats[rankId].some(slotData =>
            slotData !== null && slotData.level > 1
        );
    },

    /**
     * Update the rank level up and level down button states
     */
    updateRankLevelUpButton: function(rankId) {
        const rankLevelUpButton = document.querySelector(`.fg-honor-rank-level-up[data-rank="${rankId}"]`);
        if (rankLevelUpButton) {
            const canLevelUp = this.canRankLevelUp(rankId);
            rankLevelUpButton.disabled = !canLevelUp;
        }

        const rankLevelDownButton = document.querySelector(`.fg-honor-rank-level-down[data-rank="${rankId}"]`);
        if (rankLevelDownButton) {
            const canLevelDown = this.canRankLevelDown(rankId);
            rankLevelDownButton.disabled = !canLevelDown;
        }
    },

    /**
     * Adjust level of all medals in a rank by a given direction
     * @param {string} rankId - The rank to adjust
     * @param {number} direction - +1 for level up, -1 for level down
     */
    adjustRankLevel: function(rankId, direction) {
        let anyChanged = false;

        // Go through each slot in the rank
        this.selectedStats[rankId].forEach((slotData, i) => {
            if (!slotData) return;

            const newLevel = slotData.level + direction;

            // Check if new level is within valid range
            if (newLevel >= 1 && newLevel <= HonorMedalData.maxLevel) {
                slotData.level = newLevel;
                this.updateSlotUI(rankId, i);
                this.showLevelUpFeedback(rankId, i);
                anyChanged = true;
            }
        });

        if (anyChanged) {
            // Update selected stats display
            this.updateSelectedStatsDisplay();

            // Update stats in the main planner
            this.updateHonorMedalStats();

            // Save to store
            this.saveToStore();

            // Update button states
            this.updateRankLevelUpButton(rankId);

            // Update the rank level display
            this.updateRankLevelDisplay(rankId);
        }
    },

    /**
     * Level up all medals in a rank
     */
    levelUpRank: function(rankId) {
        this.adjustRankLevel(rankId, 1);
    },

    /**
     * Level down all medals in a rank
     */
    levelDownRank: function(rankId) {
        this.adjustRankLevel(rankId, -1);
    },

    /**
     * Level up a medal's stat
     */
    levelUpStat: function(rankId, slotIndex) {
        const slotData = this.selectedStats[rankId][slotIndex];
        if (!slotData) return;

        // Check if already at max level
        if (slotData.level >= HonorMedalData.maxLevel) return;

        // Increase level
        slotData.level += 1;

        // Update UI and stats
        this.updateSlotUI(rankId, slotIndex);
        this.updateSelectedStatsDisplay();
        this.updateHonorMedalStats();
        this.saveToStore();

        // Update level up button state
        this.updateRankLevelUpButton(rankId);

        // Show a brief animation/feedback
        this.showLevelUpFeedback(rankId, slotIndex);
    },

    /**
     * Show brief level-up animation/feedback
     */
    showLevelUpFeedback: function(rankId, slotIndex) {
        const slotElement = document.querySelector(`.fg-honor-slot[data-rank="${rankId}"][data-slot="${slotIndex}"]`);
        if (!slotElement) return;

        // Add animation class
        slotElement.classList.add('level-up-animation');

        // Remove animation class after it completes
        setTimeout(() => slotElement.classList.remove('level-up-animation'), 500);
    },

    /**
     * Get the essential data for saving
     * Returns just the minimum data needed to restore the system state
     */
    getEssentialData: function() {
        return {
            selectedStats: JSON.parse(JSON.stringify(this.selectedStats))
        };
    },

    /**
     * Save current data to the central store
     */
    saveToStore: function() {
        // Ensure data structure is valid
        this.ensureDataStructure();

        if (typeof BuildSaverStore !== 'undefined' && BuildSaverStore.saveData) {
            const essentialData = this.getEssentialData();
            BuildSaverStore.saveData('honor-medal', essentialData);
            return true;
        }
        return false;
    },

    /**
     * Load data from the central BuildSaverStore
     */
    loadFromStore: function() {
        // Check if BuildSaverStore exists and has data
        if (window.BuildSaverStore && BuildSaverStore.dataLoaded) {
            // Get data for this system
            const systemData = BuildSaverStore.getSystemData('honor-medal');
            if (systemData && systemData.selectedStats) {
                // Use the saved data
                return this.loadFromData(systemData);
            }
        }

        return false;
    },

    /**
     * Load data directly from provided data object
     * Used by both loadFromStore and the build sharing feature
     */
    loadFromData: function(data) {
        if (data && data.selectedStats) {
            // Use the saved data
            this.selectedStats = JSON.parse(JSON.stringify(data.selectedStats));

            // Ensure data structure validity
            this.ensureDataStructure();

            // Update UI to reflect the loaded data
            for (const rankId in this.selectedStats) {
                this.selectedStats[rankId].forEach((_, index) => {
                    if (index < HonorMedalData.ranks[rankId].slots) {
                        this.updateSlotUI(rankId, index);
                    }
                });
            }

            // Update summary displays
            this.updateSelectedStatsDisplay();
            this.updateHonorMedalStats();

            return true;
        }

        return false;
    },

    /**
     * Reset all honor medal stats
     * - Remove all equipped stats from all ranks
     */
    resetAllStats: function() {
        // Reset all stats for all ranks
        this.selectedStats = {
            captain: Array(4).fill(null),
            general: Array(6).fill(null),
            commander: Array(8).fill(null),
            hero: Array(10).fill(null),
            legend: Array(12).fill(null)
        };

        // Update UI for all ranks
        for (const rankId in this.selectedStats) {
            this.selectedStats[rankId].forEach((_, index) => {
                if (index < HonorMedalData.ranks[rankId].slots) {
                    this.updateSlotUI(rankId, index);
                }
            });
        }

        // Update summary displays
        this.updateSelectedStatsDisplay();
        this.updateHonorMedalStats();

        // Save the changes
        this.saveToStore();
    },

    /**
     * Calculate the average level of medals in a rank
     */
    calculateRankLevel: function(rankId) {
        const stats = this.selectedStats[rankId];
        if (!stats || !stats.length) return 0;

        // Count medals and sum levels
        let medalCount = 0;
        let totalLevels = 0;

        stats.forEach(stat => {
            if (stat) {
                medalCount++;
                totalLevels += stat.level;
            }
        });

        // Return average, or 0 if no medals
        return medalCount > 0 ? Math.floor(totalLevels / medalCount) : 0;
    },

    /**
     * Update the rank level display
     */
    updateRankLevelDisplay: function(rankId) {
        const rankHeader = document.querySelector(`#${rankId}-rank .fg-honor-rank-header`);
        if (rankHeader) {
            const rank = HonorMedalData.ranks[rankId];
            const rankLevel = this.calculateRankLevel(rankId);
            const rankLevelDisplay = rankLevel > 0 ? ` <span class="level">Lv.${rankLevel}</span>` : '';
            rankHeader.innerHTML = `• ${rank.name}${rankLevelDisplay}`;
        }
    },

    /**
     * Ensure the proper data structure exists and save
     */
    ensureAndSave: function() {
        // Ensure data structure is valid
        this.ensureDataStructure();

        // Save data to the central store
        if (typeof BuildSaverStore !== 'undefined' && BuildSaverStore.saveData) {
            const essentialData = this.getEssentialData();
            BuildSaverStore.saveData('honor-medal', essentialData);
            return true;
        }

        return false;
    },

    /**
     * Ensure the proper data structure exists
     */
    ensureDataStructure: function() {
        // Initialize selectedStats if it doesn't exist
        if (!this.selectedStats) {
            this.selectedStats = {
                captain: Array(4).fill(null),
                general: Array(6).fill(null),
                commander: Array(8).fill(null),
                hero: Array(10).fill(null),
                legend: Array(12).fill(null)
            };
        }

        // Ensure all ranks exist
        const requiredRanks = ['captain', 'general', 'commander', 'hero', 'legend'];
        requiredRanks.forEach(rankId => {
            if (!this.selectedStats[rankId]) {
                const slotCount = HonorMedalData.ranks[rankId].slots;
                this.selectedStats[rankId] = Array(slotCount).fill(null);
            }
        });

        // Ensure all arrays have correct length
        for (const rankId in this.selectedStats) {
            if (HonorMedalData.ranks[rankId]) {
                const requiredSlots = HonorMedalData.ranks[rankId].slots;
                const currentSlots = this.selectedStats[rankId].length;

                if (currentSlots < requiredSlots) {
                    // Add nulls to reach required length
                    const nullsToAdd = Array(requiredSlots - currentSlots).fill(null);
                    this.selectedStats[rankId] = this.selectedStats[rankId].concat(nullsToAdd);
                } else if (currentSlots > requiredSlots) {
                    // Trim excess slots
                    this.selectedStats[rankId] = this.selectedStats[rankId].slice(0, requiredSlots);
                }
            }
        }

        // Ensure each medal has valid properties
        for (const rankId in this.selectedStats) {
            for (let i = 0; i < this.selectedStats[rankId].length; i++) {
                const medal = this.selectedStats[rankId][i];
                if (medal) {
                    // Ensure medal has all required properties
                    if (!medal.statId) medal.statId = 'unknown';
                    if (!medal.level || typeof medal.level !== 'number') medal.level = 1;

                    // Cap level at maxLevel
                    if (medal.level > HonorMedalData.maxLevel) {
                        medal.level = HonorMedalData.maxLevel;
                    }
                }
            }
        }
    }
};

// Safety check to ensure the system is available when needed
document.addEventListener('DOMContentLoaded', function() {
    // Don't auto-initialize - let the BuildPlanner handle initialization
    // when the system tab is clicked instead
});